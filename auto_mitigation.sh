#!/bin/bash

# Script de Mitigación Automática para Tráfico Anómalo
# USAR CON PRECAUCIÓN - Puede bloquear IPs legítimas
# Uso: bash auto_mitigation.sh [--dry-run]

DRY_RUN=false
if [ "$1" = "--dry-run" ]; then
    DRY_RUN=true
    echo "MODO DRY-RUN: Solo mostrará qué acciones tomaría sin ejecutarlas"
fi

echo "=========================================="
echo "SCRIPT DE MITIGACIÓN AUTOMÁTICA"
echo "Fecha: $(date)"
echo "=========================================="

# Configuración de umbrales
MAX_CONNECTIONS_PER_IP=30
MAX_CPU_USAGE=85
MAX_REQUESTS_PER_MINUTE=100

# Función para ejecutar comandos
execute_command() {
    local cmd="$1"
    local description="$2"
    
    if [ "$DRY_RUN" = true ]; then
        echo "[DRY-RUN] $description"
        echo "  Comando: $cmd"
    else
        echo "Ejecutando: $description"
        eval "$cmd"
        if [ $? -eq 0 ]; then
            echo "✅ Completado"
        else
            echo "❌ Error al ejecutar"
        fi
    fi
}

# Función para crear backup de iptables
backup_iptables() {
    if [ "$DRY_RUN" = false ]; then
        iptables-save > /tmp/iptables_backup_$(date +%Y%m%d_%H%M%S).txt
        echo "✅ Backup de iptables creado en /tmp/"
    fi
}

echo -e "\n1. ANÁLISIS DE AMENAZAS"
echo "----------------------------------------"

# Detectar IPs con demasiadas conexiones
echo "Analizando conexiones por IP..."
SUSPICIOUS_IPS=$(ss -tn | awk 'NR>1 {print $4}' | cut -d: -f1 | sort | uniq -c | sort -nr | awk -v max=$MAX_CONNECTIONS_PER_IP '$1 > max {print $2}')

if [ ! -z "$SUSPICIOUS_IPS" ]; then
    echo "🚨 IPs sospechosas detectadas:"
    ss -tn | awk 'NR>1 {print $4}' | cut -d: -f1 | sort | uniq -c | sort -nr | awk -v max=$MAX_CONNECTIONS_PER_IP '$1 > max {print "  " $2 " (" $1 " conexiones)"}'
else
    echo "✅ No se detectaron IPs con conexiones excesivas"
fi

# Detectar procesos con alto CPU
echo -e "\nAnalizando uso de CPU..."
HIGH_CPU_PROCESSES=$(ps aux --sort=-%cpu | awk -v max=$MAX_CPU_USAGE 'NR>1 && $3 > max {print $2 ":" $11 ":" $3}')

if [ ! -z "$HIGH_CPU_PROCESSES" ]; then
    echo "🚨 Procesos con alto CPU detectados:"
    echo "$HIGH_CPU_PROCESSES" | while IFS=':' read pid process cpu; do
        echo "  PID $pid: $process ($cpu% CPU)"
    done
else
    echo "✅ Uso de CPU normal"
fi

# Analizar logs web si existen
WEB_LOG=""
if [ -f "/var/log/nginx/access.log" ]; then
    WEB_LOG="/var/log/nginx/access.log"
elif [ -f "/var/log/apache2/access.log" ]; then
    WEB_LOG="/var/log/apache2/access.log"
elif [ -f "/var/log/httpd/access_log" ]; then
    WEB_LOG="/var/log/httpd/access_log"
fi

if [ ! -z "$WEB_LOG" ] && [ -f "$WEB_LOG" ]; then
    echo -e "\nAnalizando logs web..."
    CURRENT_MINUTE=$(date '+%d/%b/%Y:%H:%M')
    REQUESTS_THIS_MINUTE=$(grep "$CURRENT_MINUTE" "$WEB_LOG" 2>/dev/null | wc -l)
    
    if [ $REQUESTS_THIS_MINUTE -gt $MAX_REQUESTS_PER_MINUTE ]; then
        echo "🚨 Tráfico web anómalo: $REQUESTS_THIS_MINUTE requests en el último minuto"
        
        # Detectar IPs con más requests
        SPAM_IPS=$(grep "$CURRENT_MINUTE" "$WEB_LOG" 2>/dev/null | awk '{print $1}' | sort | uniq -c | sort -nr | awk '$1 > 20 {print $2}')
        if [ ! -z "$SPAM_IPS" ]; then
            echo "  IPs con spam de requests:"
            grep "$CURRENT_MINUTE" "$WEB_LOG" 2>/dev/null | awk '{print $1}' | sort | uniq -c | sort -nr | awk '$1 > 20 {print "    " $2 " (" $1 " requests)"}'
        fi
    else
        echo "✅ Tráfico web normal: $REQUESTS_THIS_MINUTE requests en el último minuto"
    fi
fi

echo -e "\n2. APLICANDO MITIGACIONES"
echo "----------------------------------------"

# Crear backup antes de hacer cambios
if [ ! -z "$SUSPICIOUS_IPS" ] || [ ! -z "$HIGH_CPU_PROCESSES" ]; then
    backup_iptables
fi

# Bloquear IPs sospechosas
if [ ! -z "$SUSPICIOUS_IPS" ]; then
    echo -e "\nBloqueando IPs sospechosas..."
    echo "$SUSPICIOUS_IPS" | while read ip; do
        # Verificar que no sea IP local
        if [[ ! "$ip" =~ ^(127\.|10\.|172\.(1[6-9]|2[0-9]|3[01])\.|192\.168\.) ]]; then
            execute_command "iptables -A INPUT -s $ip -j DROP" "Bloqueando IP: $ip"
        else
            echo "⚠️  Saltando IP local/privada: $ip"
        fi
    done
fi

# Aplicar rate limiting general si no existe
echo -e "\nAplicando rate limiting..."
RATE_LIMIT_EXISTS=$(iptables -L | grep "limit:")
if [ -z "$RATE_LIMIT_EXISTS" ]; then
    execute_command "iptables -A INPUT -p tcp --dport 80 -m connlimit --connlimit-above 20 -j DROP" "Rate limiting HTTP (máx 20 conexiones por IP)"
    execute_command "iptables -A INPUT -p tcp --dport 443 -m connlimit --connlimit-above 20 -j DROP" "Rate limiting HTTPS (máx 20 conexiones por IP)"
    execute_command "iptables -A INPUT -p tcp --dport 22 -m connlimit --connlimit-above 3 -j DROP" "Rate limiting SSH (máx 3 conexiones por IP)"
else
    echo "✅ Rate limiting ya configurado"
fi

# Matar procesos con alto CPU si es necesario
if [ ! -z "$HIGH_CPU_PROCESSES" ]; then
    echo -e "\nGestionando procesos con alto CPU..."
    echo "$HIGH_CPU_PROCESSES" | while IFS=':' read pid process cpu; do
        # Solo matar procesos específicos conocidos como problemáticos
        case "$process" in
            *bitcoin*|*miner*|*crypto*|*ddos*|*flood*)
                execute_command "kill -9 $pid" "Matando proceso sospechoso: $process (PID: $pid)"
                ;;
            *)
                echo "⚠️  Proceso con alto CPU detectado pero no matado automáticamente: $process (PID: $pid)"
                echo "    Revisar manualmente si es necesario"
                ;;
        esac
    done
fi

echo -e "\n3. CONFIGURACIONES ADICIONALES"
echo "----------------------------------------"

# Configurar límites del sistema si no están configurados
echo "Verificando límites del sistema..."

# Verificar y configurar límites de archivos abiertos
CURRENT_ULIMIT=$(ulimit -n)
if [ $CURRENT_ULIMIT -lt 65536 ]; then
    execute_command "ulimit -n 65536" "Aumentando límite de archivos abiertos"
fi

# Configurar parámetros de red del kernel
echo "Configurando parámetros de red..."
execute_command "echo 1 > /proc/sys/net/ipv4/tcp_syncookies" "Habilitando SYN cookies"
execute_command "echo 1024 > /proc/sys/net/core/somaxconn" "Aumentando cola de conexiones"
execute_command "echo 30 > /proc/sys/net/ipv4/tcp_fin_timeout" "Reduciendo timeout FIN"

echo -e "\n4. MONITOREO POST-MITIGACIÓN"
echo "----------------------------------------"

sleep 5  # Esperar un poco para que los cambios tomen efecto

echo "Estado actual después de mitigaciones:"
CURRENT_CONNECTIONS=$(ss -tn | wc -l)
CURRENT_ESTABLISHED=$(ss -tn | grep ESTABLISHED | wc -l)
CURRENT_CPU=$(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | cut -d'%' -f1)

echo "  Conexiones totales: $CURRENT_CONNECTIONS"
echo "  Conexiones establecidas: $CURRENT_ESTABLISHED"
echo "  Uso de CPU: $CURRENT_CPU%"

echo -e "\n5. COMANDOS PARA REVERTIR CAMBIOS"
echo "----------------------------------------"
echo "Si necesitas revertir los cambios:"
echo "  # Restaurar iptables:"
echo "  iptables-restore < /tmp/iptables_backup_*.txt"
echo ""
echo "  # Ver reglas actuales:"
echo "  iptables -L -n"
echo ""
echo "  # Eliminar todas las reglas (CUIDADO):"
echo "  iptables -F"

echo -e "\n=========================================="
echo "MITIGACIÓN COMPLETADA"
echo "Revisa los logs y monitorea el servidor"
echo "=========================================="
