#!/bin/bash

# Monitor en Tiempo Real para Detectar Anomalías de Tráfico
# Uso: bash real_time_monitor.sh

echo "=========================================="
echo "MONITOR EN TIEMPO REAL - TRÁFICO SERVIDOR"
echo "Presiona Ctrl+C para detener"
echo "=========================================="

# Función para limpiar al salir
cleanup() {
    echo -e "\n\nMonitoreo detenido."
    exit 0
}

trap cleanup SIGINT

# Detectar log de servidor web
if [ -f "/var/log/nginx/access.log" ]; then
    WEB_LOG="/var/log/nginx/access.log"
elif [ -f "/var/log/apache2/access.log" ]; then
    WEB_LOG="/var/log/apache2/access.log"
elif [ -f "/var/log/httpd/access_log" ]; then
    WEB_LOG="/var/log/httpd/access_log"
else
    WEB_LOG=""
fi

# Contador de iteraciones
COUNTER=0

while true; do
    clear
    COUNTER=$((COUNTER + 1))
    
    echo "=========================================="
    echo "MONITOR TIEMPO REAL - Iteración #$COUNTER"
    echo "Hora: $(date)"
    echo "=========================================="
    
    echo -e "\n🔥 TOP 10 IPs CON MÁS CONEXIONES ACTIVAS:"
    echo "----------------------------------------"
    ss -tn | awk 'NR>1 {print $4}' | cut -d: -f1 | sort | uniq -c | sort -nr | head -10 | while read count ip; do
        if [ $count -gt 20 ]; then
            echo "⚠️  $count conexiones desde $ip (SOSPECHOSO)"
        else
            echo "   $count conexiones desde $ip"
        fi
    done
    
    echo -e "\n📊 ESTADÍSTICAS DE CONEXIONES:"
    echo "----------------------------------------"
    TOTAL_CONN=$(ss -tn | wc -l)
    ESTABLISHED=$(ss -tn | grep ESTABLISHED | wc -l)
    TIME_WAIT=$(ss -tn | grep TIME-WAIT | wc -l)
    
    echo "Total conexiones: $TOTAL_CONN"
    echo "Establecidas: $ESTABLISHED"
    echo "En TIME-WAIT: $TIME_WAIT"
    
    if [ $ESTABLISHED -gt 100 ]; then
        echo "⚠️  ALERTA: Demasiadas conexiones establecidas ($ESTABLISHED)"
    fi
    
    echo -e "\n💻 USO DE RECURSOS:"
    echo "----------------------------------------"
    CPU_USAGE=$(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | cut -d'%' -f1)
    MEM_USAGE=$(free | grep Mem | awk '{printf "%.1f", $3/$2 * 100.0}')
    
    echo "CPU: ${CPU_USAGE}%"
    echo "Memoria: ${MEM_USAGE}%"
    
    if (( $(echo "$CPU_USAGE > 80" | bc -l) )); then
        echo "⚠️  ALERTA: Alto uso de CPU"
    fi
    
    echo -e "\n🌐 PROCESOS DE RED TOP 5:"
    echo "----------------------------------------"
    lsof -i -n | awk '{print $2, $1}' | sort | uniq -c | sort -nr | head -5
    
    if [ ! -z "$WEB_LOG" ] && [ -f "$WEB_LOG" ]; then
        echo -e "\n📝 ÚLTIMAS 5 REQUESTS:"
        echo "----------------------------------------"
        tail -5 "$WEB_LOG" | while read line; do
            IP=$(echo "$line" | awk '{print $1}')
            URL=$(echo "$line" | awk '{print $7}')
            STATUS=$(echo "$line" | awk '{print $9}')
            echo "$IP -> $URL ($STATUS)"
        done
        
        echo -e "\n🔍 REQUESTS POR MINUTO (últimos 5 min):"
        echo "----------------------------------------"
        for i in {0..4}; do
            MINUTE=$(date -d "$i minutes ago" '+%d/%b/%Y:%H:%M')
            COUNT=$(grep "$MINUTE" "$WEB_LOG" 2>/dev/null | wc -l)
            echo "$(date -d "$i minutes ago" '+%H:%M'): $COUNT requests"
        done
    fi
    
    echo -e "\n🚨 ALERTAS AUTOMÁTICAS:"
    echo "----------------------------------------"
    
    # Detectar IPs con demasiadas conexiones
    SUSPICIOUS_IP=$(ss -tn | awk 'NR>1 {print $4}' | cut -d: -f1 | sort | uniq -c | sort -nr | head -1)
    CONN_COUNT=$(echo $SUSPICIOUS_IP | awk '{print $1}')
    IP_ADDR=$(echo $SUSPICIOUS_IP | awk '{print $2}')
    
    if [ $CONN_COUNT -gt 50 ]; then
        echo "🚨 IP SOSPECHOSA: $IP_ADDR con $CONN_COUNT conexiones"
        echo "   Comando para bloquear: iptables -A INPUT -s $IP_ADDR -j DROP"
    else
        echo "✅ No se detectaron IPs con conexiones excesivas"
    fi
    
    # Detectar procesos con alto CPU
    HIGH_CPU=$(ps aux --sort=-%cpu | awk 'NR==2 {print $3, $11}')
    CPU_PERCENT=$(echo $HIGH_CPU | awk '{print $1}')
    PROCESS_NAME=$(echo $HIGH_CPU | awk '{print $2}')
    
    if (( $(echo "$CPU_PERCENT > 80" | bc -l) )); then
        echo "🚨 PROCESO CON ALTO CPU: $PROCESS_NAME ($CPU_PERCENT%)"
    else
        echo "✅ Uso de CPU normal"
    fi
    
    echo -e "\n⏱️  Próxima actualización en 10 segundos..."
    echo "   Presiona Ctrl+C para detener el monitoreo"
    
    sleep 10
done
