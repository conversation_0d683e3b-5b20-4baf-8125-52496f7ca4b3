#!/bin/bash

# Script de Diagnóstico de Servidor - Análisis de Tráfico Alto
# Uso: bash server_diagnostics.sh

echo "=========================================="
echo "DIAGNÓSTICO DE SERVIDOR - TRÁFICO ALTO"
echo "Fecha: $(date)"
echo "=========================================="

# Crear directorio para logs
mkdir -p /tmp/server_diagnostics
LOG_DIR="/tmp/server_diagnostics"

echo -e "\n1. INFORMACIÓN DEL SISTEMA"
echo "----------------------------------------"
echo "Hostname: $(hostname)"
echo "Uptime: $(uptime)"
echo "Usuarios conectados: $(who | wc -l)"

echo -e "\n2. USO DE RECURSOS"
echo "----------------------------------------"
echo "=== CPU y Memoria ==="
top -bn1 | head -20

echo -e "\n=== Uso de Disco ==="
df -h

echo -e "\n=== Procesos que más CPU usan ==="
ps aux --sort=-%cpu | head -10

echo -e "\n=== Procesos que más memoria usan ==="
ps aux --sort=-%mem | head -10

echo -e "\n3. ANÁLISIS DE RED"
echo "----------------------------------------"
echo "=== Puertos en escucha ==="
ss -tuln | grep LISTEN

echo -e "\n=== Conexiones activas por estado ==="
ss -s

echo -e "\n=== Top 20 IPs con más conexiones ==="
ss -tn | awk 'NR>1 {print $4}' | cut -d: -f1 | sort | uniq -c | sort -nr | head -20

echo -e "\n=== Conexiones por puerto ==="
ss -tn | awk 'NR>1 {print $4}' | cut -d: -f2 | sort | uniq -c | sort -nr

echo -e "\n4. ANÁLISIS DE LOGS WEB"
echo "----------------------------------------"

# Detectar tipo de servidor web
if [ -f "/var/log/nginx/access.log" ]; then
    WEB_LOG="/var/log/nginx/access.log"
    echo "Servidor detectado: Nginx"
elif [ -f "/var/log/apache2/access.log" ]; then
    WEB_LOG="/var/log/apache2/access.log"
    echo "Servidor detectado: Apache"
elif [ -f "/var/log/httpd/access_log" ]; then
    WEB_LOG="/var/log/httpd/access_log"
    echo "Servidor detectado: Apache (CentOS/RHEL)"
else
    echo "No se encontraron logs de servidor web estándar"
    WEB_LOG=""
fi

if [ ! -z "$WEB_LOG" ] && [ -f "$WEB_LOG" ]; then
    echo -e "\n=== Últimas 20 entradas del log ==="
    tail -20 "$WEB_LOG"
    
    echo -e "\n=== Top 20 IPs con más requests (última hora) ==="
    awk -v date="$(date -d '1 hour ago' '+%d/%b/%Y:%H')" '$4 > "["date {print $1}' "$WEB_LOG" | sort | uniq -c | sort -nr | head -20
    
    echo -e "\n=== URLs más solicitadas (últimas 1000 entradas) ==="
    tail -1000 "$WEB_LOG" | awk '{print $7}' | sort | uniq -c | sort -nr | head -15
    
    echo -e "\n=== User Agents sospechosos ==="
    tail -1000 "$WEB_LOG" | awk -F'"' '{print $6}' | sort | uniq -c | sort -nr | head -10
    
    echo -e "\n=== Códigos de respuesta ==="
    tail -1000 "$WEB_LOG" | awk '{print $9}' | sort | uniq -c | sort -nr
fi

echo -e "\n5. ANÁLISIS DE TRÁFICO DE RED"
echo "----------------------------------------"
echo "=== Estadísticas de interfaces de red ==="
cat /proc/net/dev

echo -e "\n=== Conexiones establecidas por IP remota ==="
netstat -tn | awk '$6 == "ESTABLISHED" {print $5}' | cut -d: -f1 | sort | uniq -c | sort -nr | head -15

echo -e "\n6. PROCESOS SOSPECHOSOS"
echo "----------------------------------------"
echo "=== Procesos con muchas conexiones de red ==="
lsof -i -n | awk '{print $2, $1}' | sort | uniq -c | sort -nr | head -10

echo -e "\n=== Procesos escuchando en puertos ==="
lsof -i -P -n | grep LISTEN

echo -e "\n7. ANÁLISIS DE SEGURIDAD"
echo "----------------------------------------"
echo "=== Intentos de login fallidos (últimas 50) ==="
grep "Failed password" /var/log/auth.log 2>/dev/null | tail -50 || echo "Log de auth no encontrado"

echo -e "\n=== Conexiones SSH activas ==="
who

echo -e "\n8. RECOMENDACIONES INMEDIATAS"
echo "----------------------------------------"

# Detectar posibles problemas
SUSPICIOUS_IPS=$(ss -tn | awk 'NR>1 {print $4}' | cut -d: -f1 | sort | uniq -c | sort -nr | head -1 | awk '$1 > 50 {print $2}')

if [ ! -z "$SUSPICIOUS_IPS" ]; then
    echo "⚠️  ALERTA: IP con más de 50 conexiones detectada: $SUSPICIOUS_IPS"
    echo "   Comando para bloquear: iptables -A INPUT -s $SUSPICIOUS_IPS -j DROP"
fi

HIGH_CPU_PROCESS=$(ps aux --sort=-%cpu | awk 'NR==2 {if($3>80) print $11}')
if [ ! -z "$HIGH_CPU_PROCESS" ]; then
    echo "⚠️  ALERTA: Proceso con alto uso de CPU: $HIGH_CPU_PROCESS"
fi

echo -e "\n=== Comandos útiles para mitigación ==="
echo "# Limitar conexiones por IP:"
echo "iptables -A INPUT -p tcp --dport 80 -m connlimit --connlimit-above 20 -j DROP"
echo ""
echo "# Ver conexiones en tiempo real:"
echo "watch 'ss -tn | awk \"NR>1 {print \$4}\" | cut -d: -f1 | sort | uniq -c | sort -nr | head -10'"
echo ""
echo "# Monitorear tráfico:"
echo "iftop -i eth0"

echo -e "\n=========================================="
echo "DIAGNÓSTICO COMPLETADO"
echo "Logs guardados en: $LOG_DIR"
echo "=========================================="

# Guardar resumen en archivo
{
    echo "RESUMEN DE DIAGNÓSTICO - $(date)"
    echo "================================"
    echo "Conexiones activas: $(ss -tn | wc -l)"
    echo "Procesos corriendo: $(ps aux | wc -l)"
    echo "Uso de CPU: $(top -bn1 | grep "Cpu(s)" | awk '{print $2}')"
    echo "Uso de memoria: $(free | grep Mem | awk '{printf "%.1f%%", $3/$2 * 100.0}')"
    if [ ! -z "$WEB_LOG" ] && [ -f "$WEB_LOG" ]; then
        echo "Requests última hora: $(awk -v date=\"$(date -d '1 hour ago' '+%d/%b/%Y:%H')\" '$4 > \"[\"date' "$WEB_LOG" | wc -l)"
    fi
} > "$LOG_DIR/resumen_$(date +%Y%m%d_%H%M%S).txt"

echo "Resumen guardado en: $LOG_DIR/resumen_$(date +%Y%m%d_%H%M%S).txt"
